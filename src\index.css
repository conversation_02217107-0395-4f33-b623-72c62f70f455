@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Only non-theme specific variables that don't change */
    --radius: 0.5rem;
    
    /* Default theme variables - will be overridden by theme provider */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --primary: 222.2 84% 4.9%;
    --primary-foreground: 210 40% 98%;
    
    /* Essential table variables - max 5 for strict guideline compliance */
    --table-frozen-shadow: hsl(var(--muted-foreground) / 0.2);
    --table-resize-handle-opacity: 0.5;
    --table-row-height: 48px;
    --table-header-height: 48px;
    --table-selection-width: 48px;
    
    /* Global shadow system - semantic elevation levels - ENHANCED for visibility */
    --shadow-sm: 0 2px 8px 0 rgba(0,0,0,0.25), 0 1px 4px 0 rgba(0,0,0,0.3);
    --shadow-md: 0 4px 12px 0 rgba(0,0,0,0.3), 0 2px 6px 0 rgba(0,0,0,0.35);
    --shadow-lg: 0 8px 24px 0 rgba(0,0,0,0.35), 0 4px 12px 0 rgba(0,0,0,0.4);
    --shadow-header: 0 3px 8px 0 rgba(0,0,0,0.25), 0 1px 4px 0 rgba(0,0,0,0.2);
    
    /* Badge colors */
    --badge-grade-a: #d1fae5;
    --badge-grade-a-foreground: #065f46;
    --badge-grade-a-border: #a7f3d0;
    --badge-grade-b: #e0f2fe;
    --badge-grade-b-foreground: #0369a1;
    --badge-grade-b-border: #bae6fd;
    --badge-grade-c: #fef3c7;
    --badge-grade-c-foreground: #b45309;
    --badge-grade-c-border: #fde68a;
    --badge-grade-d: #fed7aa;
    --badge-grade-d-foreground: #c2410c;
    --badge-grade-d-border: #fdba74;
    --badge-grade-f: #fee2e2;
    --badge-grade-f-foreground: #dc2626;
    --badge-grade-f-border: #fecaca;
    --badge-neutral: #f3f4f6;
    --badge-neutral-foreground: #374151;
    --badge-neutral-border: #e5e7eb;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family-primary);
    transition: background-color 0.3s, color 0.3s;
  }
}

/* Advanced Data Table Utilities - following guidelines */
@layer utilities {

  /* Advanced Data Table Utilities */
  .table-frozen-column {
    position: sticky;
    z-index: 10;
    background-color: inherit;
  }

  .table-frozen-shadow-light {
    box-shadow: 1px 0 3px rgba(0,0,0,0.1);
  }

  .table-frozen-shadow-medium {
    box-shadow: 2px 0 4px rgba(0,0,0,0.15);
  }

  .table-frozen-shadow-heavy {
    box-shadow: 3px 0 6px rgba(0,0,0,0.2);
  }

  /* Table layout consistency for frozen columns */
  table {
    border-collapse: separate;
    border-spacing: 0;
  }
  
  /* Auto-sizing table layout - default for all tables */
  table {
    table-layout: auto;
  }

  /* Allow content to determine cell size naturally */
  table td {
    position: relative;
    vertical-align: middle;
    white-space: nowrap;
    padding: 8px 12px;
    min-width: fit-content;
  }
  
  table th {
    white-space: nowrap;
    padding: 12px;
  }

  /* Column resizing handle */
  .table-resize-handle {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 4px;
    cursor: col-resize;
    user-select: none;
    touch-action: none;
  }
  
  .table-resize-handle:hover,
  .table-resize-handle.resizing {
    background-color: hsl(var(--primary));
    opacity: var(--table-resize-handle-opacity);
  }

  /* Inline editing styles */
  .table-cell-editing {
    padding: 0 !important;
  }
  
  .table-cell-editing input {
    border: 2px solid hsl(var(--primary));
    border-radius: 4px;
  }

  /* Drag and drop column reordering */
  .table-header-dragging {
    opacity: 0.5;
    cursor: grabbing;
  }
  
  .table-header-drag-over {
    border-left: 2px solid hsl(var(--primary));
  }

  th, td {
    box-sizing: border-box;
    border-right: 1px solid hsl(var(--border));
  }

  th:last-child, td:last-child {
    border-right: none;
  }

  /* Mobile touch optimization */
  .table-touch-optimized {
    min-height: 44px;
  }

  .table-touch-optimized td,
  .table-touch-optimized th {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* Mobile responsive utilities */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-text-scale {
    font-size: 14px;
    line-height: 1.4;
  }

  @media (min-width: 640px) {
    .mobile-text-scale {
      font-size: 16px;
      line-height: 1.5;
    }
  }

  /* Mobile-first button styling */
  .btn-mobile {
    @apply min-h-[44px] px-4 py-2 text-sm;
  }

  @media (min-width: 640px) {
    .btn-mobile {
      @apply min-h-[40px] px-6 py-2 text-base;
    }
  }

  /* Prevent horizontal overflow on mobile */
  .mobile-no-overflow {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Ensure touch targets meet minimum size */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Flexible button layouts for mobile */
  .mobile-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .mobile-button-group button {
    flex: 1;
    min-width: 0;
    min-height: 44px;
  }

  @media (min-width: 640px) {
    .mobile-button-group button {
      flex: initial;
    }
  }

  /* Mobile navigation spacing */
  .nav-mobile {
    padding: 8px 12px;
  }

  @media (min-width: 640px) {
    .nav-mobile {
      padding: 12px 16px;
    }
  }

  /* Responsive container padding */
  .container-responsive {
    @apply px-3 sm:px-6;
  }

  /* Mobile card spacing */
  .card-mobile {
    @apply p-3 sm:p-6;
  }

  /* Enhanced mobile responsiveness */
  @media (max-width: 1024px) {
    .lg\:hidden {
      display: none !important;
    }
  }

  @media (max-width: 640px) {
    .sm\:hidden {
      display: none !important;
    }
    
    /* Force text truncation on mobile */
    .mobile-truncate {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 150px;
    }
    
    /* Mobile grid adjustments */
    .mobile-grid-1 {
      grid-template-columns: 1fr !important;
    }
    
    .mobile-grid-2 {
      grid-template-columns: repeat(2, 1fr) !important;
    }
    
    /* Mobile spacing reduction */
    .mobile-space-y-2 > * + * {
      margin-top: 0.5rem !important;
    }
    
    /* Mobile padding reduction */
    .mobile-p-3 {
      padding: 0.75rem !important;
    }
  }

  /* Responsive table utilities */
  @media (max-width: 768px) {
    .table-mobile-stack {
      display: block;
    }

    .table-mobile-stack thead,
    .table-mobile-stack tbody,
    .table-mobile-stack th,
    .table-mobile-stack td,
    .table-mobile-stack tr {
      display: block;
    }

    .table-mobile-stack thead tr {
      position: absolute;
      top: -9999px;
      left: -9999px;
    }

    .table-mobile-stack tr {
      border: 1px solid hsl(var(--border));
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 8px;
      background: hsl(var(--background));
    }

    .table-mobile-stack td {
      border: none;
      position: relative;
      padding-left: 50% !important;
      text-align: left;
    }

    .table-mobile-stack td:before {
      content: attr(data-label);
      position: absolute;
      left: 6px;
      width: 45%;
      padding-right: 10px;
      white-space: nowrap;
      font-weight: 600;
      color: hsl(var(--muted-foreground));
    }
  }
}
