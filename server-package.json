{"name": "sgs-alumni-backend", "version": "1.0.0", "description": "Backend API server for SGS Alumni application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mysql", "api", "backend", "alumni"], "author": "SGS Alumni Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}