{"name": "sgs-gita-alumni", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "prepare": "husky", "check-redundancy": "jscpd", "analyze-bundle": "ANALYZE=true npm run build", "quality-check": "npm run lint && npm run check-redundancy", "validate-docs": "node scripts/validate-documentation-standards.js", "validate-docs-standards": "node scripts/validate-documentation-standards.js"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.883.0", "@aws-sdk/lib-dynamodb": "^3.883.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.13", "@sentry/browser": "^10.10.0", "@sentry/react": "^10.10.0", "@sentry/replay": "^7.116.0", "@sentry/tracing": "^7.120.4", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^17.2.2", "express": "^5.1.0", "lucide-react": "^0.441.0", "mysql2": "^3.14.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "sgs-gita-alumni": "file:", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.3.1", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.12", "eslint-plugin-sonarjs": "^3.0.5", "globals": "^16.3.0", "husky": "^9.1.7", "jscpd": "^4.0.5", "jsdom": "^26.1.0", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "typescript": "^5.6.3", "vite": "^5.4.9", "vitest": "^3.2.4", "webpack-bundle-analyzer": "^4.10.2"}}