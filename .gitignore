# Python  
__pycache__/  
*.py[cod]  
*$py.class  
*.so  
.Python  
build/  
develop-eggs/  
dist/  
downloads/  
eggs/  
.eggs/  
lib64/  
parts/  
sdist/  
var/  
wheels/  
*.egg-info/  
.installed.cfg  
*.egg  
# Virtualenv  
ENV/  
env/  
ENV.bak/  
venv/  
ENV/  
env.bak/  
# Pip  
pip-log.txt  
pip-delete-this-directory.txt  
# Tox  
.tox/  
# Django  
*.log  
# .env  
.env  
# Data files  
*.csv  
*.xls  
*.xlsx  
# Frontend (React/Vite)  
node_modules/  
dist/  
build/  
.env  
.env.local  
.env.development.local  
.env.test.local  
.env.production.local  
npm-debug.log*  
yarn-debug.log*  
yarn-error.log*  
# IDE  
.vscode/  
.idea/  
# OS  
.DS_Store  
Thumbs.db 
